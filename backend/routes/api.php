<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\TicketController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\SlaController;
use App\Http\Controllers\UserController;

/*
|--------------------------------------------------------------------------
| Swagger Documentation Route
|--------------------------------------------------------------------------
*/
Route::get('/documentation', function () {
    return view('swagger.index');
})->name('api.documentation');

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Authentication routes
Route::prefix('auth')->group(function () {
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
    Route::post('logout', [AuthController::class, 'logout'])->middleware('jwt.auth');
    Route::get('me', [AuthController::class, 'me'])->middleware('jwt.auth');
    Route::post('refresh', [AuthController::class, 'refresh'])->middleware('jwt.auth');
});

// Protected routes
Route::middleware(['jwt.auth'])->group(function () {
    
    // Ticket routes
    Route::apiResource('tickets', TicketController::class);
    Route::post('tickets/{id}/comment', [TicketController::class, 'addComment']);
    Route::post('tickets/{id}/attachment', [TicketController::class, 'addAttachment']);
    
    // Service routes
    Route::apiResource('services', ServiceController::class);
    
    // SLA routes
    Route::apiResource('slas', SlaController::class);
    
    // User routes (Admin only for most operations)
    Route::apiResource('users', UserController::class);
    
    // Reports routes (Manager and Admin only)
    Route::prefix('reports')->middleware('permission:view_reports')->group(function () {
        Route::get('dashboard', function () {
            $totalTickets = \App\Models\Ticket::count();
            $openTickets = \App\Models\Ticket::whereIn('status', ['New', 'In Progress', 'Pending'])->count();
            $resolvedTickets = \App\Models\Ticket::where('status', 'Resolved')->count();
            $closedTickets = \App\Models\Ticket::where('status', 'Closed')->count();
            
            $ticketsByPriority = \App\Models\Ticket::selectRaw('priority, count(*) as count')
                ->groupBy('priority')
                ->pluck('count', 'priority');
                
            $ticketsByStatus = \App\Models\Ticket::selectRaw('status, count(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status');
            
            return response()->json([
                'total_tickets' => $totalTickets,
                'open_tickets' => $openTickets,
                'resolved_tickets' => $resolvedTickets,
                'closed_tickets' => $closedTickets,
                'tickets_by_priority' => $ticketsByPriority,
                'tickets_by_status' => $ticketsByStatus,
            ]);
        });
        
        Route::get('mtta', function () {
            // Mean Time To Acknowledge
            $avgResponseTime = \App\Models\TicketUpdate::where('type', 'status_change')
                ->whereJsonContains('meta_json->new_value', 'In Progress')
                ->join('tickets', 'ticket_updates.ticket_id', '=', 'tickets.id')
                ->selectRaw('AVG(TIMESTAMPDIFF(MINUTE, tickets.created_at, ticket_updates.created_at)) as avg_minutes')
                ->value('avg_minutes');
                
            return response()->json([
                'mtta_minutes' => round($avgResponseTime ?? 0, 2),
                'mtta_hours' => round(($avgResponseTime ?? 0) / 60, 2),
            ]);
        });
        
        Route::get('mttr', function () {
            // Mean Time To Resolution
            $avgResolutionTime = \App\Models\TicketUpdate::where('type', 'status_change')
                ->whereJsonContains('meta_json->new_value', 'Resolved')
                ->join('tickets', 'ticket_updates.ticket_id', '=', 'tickets.id')
                ->selectRaw('AVG(TIMESTAMPDIFF(MINUTE, tickets.created_at, ticket_updates.created_at)) as avg_minutes')
                ->value('avg_minutes');
                
            return response()->json([
                'mttr_minutes' => round($avgResolutionTime ?? 0, 2),
                'mttr_hours' => round(($avgResolutionTime ?? 0) / 60, 2),
            ]);
        });
        
        Route::get('sla-compliance', function () {
            $tickets = \App\Models\Ticket::with(['sla', 'updates'])
                ->whereNotNull('sla_id')
                ->where('status', 'Resolved')
                ->get();
                
            $compliant = 0;
            $total = $tickets->count();
            
            foreach ($tickets as $ticket) {
                $resolvedUpdate = $ticket->updates()
                    ->where('type', 'status_change')
                    ->whereJsonContains('meta_json->new_value', 'Resolved')
                    ->first();
                    
                if ($resolvedUpdate && $ticket->sla) {
                    $resolutionTime = $ticket->created_at->diffInMinutes($resolvedUpdate->created_at);
                    if ($resolutionTime <= $ticket->sla->target_resolution_mins) {
                        $compliant++;
                    }
                }
            }
            
            $complianceRate = $total > 0 ? ($compliant / $total) * 100 : 0;
            
            return response()->json([
                'total_tickets' => $total,
                'compliant_tickets' => $compliant,
                'compliance_rate' => round($complianceRate, 2),
            ]);
        });
    });
    
    // Roles routes (Admin only)
    Route::prefix('roles')->middleware('permission:manage_users')->group(function () {
        Route::get('/', function () {
            return response()->json(['roles' => \App\Models\Role::all()]);
        });
    });
    
    // Audit logs routes (Admin only)
    Route::prefix('audit-logs')->middleware('permission:full_access')->group(function () {
        Route::get('/', function (Request $request) {
            $query = \App\Models\AuditLog::with('actor');
            
            if ($request->has('entity')) {
                $query->where('entity', $request->entity);
            }
            
            if ($request->has('entity_id')) {
                $query->where('entity_id', $request->entity_id);
            }
            
            if ($request->has('action')) {
                $query->where('action', $request->action);
            }
            
            $logs = $query->orderBy('created_at', 'desc')->paginate(50);
            
            return response()->json(['audit_logs' => $logs]);
        });
    });
});
