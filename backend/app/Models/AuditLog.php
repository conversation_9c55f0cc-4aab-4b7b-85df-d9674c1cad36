<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AuditLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'actor_id',
        'entity',
        'entity_id',
        'action',
        'payload_json',
    ];

    protected $casts = [
        'payload_json' => 'array',
    ];

    /**
     * Get the user who performed the action.
     */
    public function actor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'actor_id');
    }

    /**
     * Scope a query to only include logs for a specific entity.
     */
    public function scopeForEntity($query, $entity, $entityId = null)
    {
        $query = $query->where('entity', $entity);
        
        if ($entityId) {
            $query = $query->where('entity_id', $entityId);
        }
        
        return $query;
    }

    /**
     * Scope a query to only include logs for a specific action.
     */
    public function scopeForAction($query, $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Create an audit log entry.
     */
    public static function log(string $entity, int $entityId, string $action, array $payload = [], ?int $actorId = null): self
    {
        return self::create([
            'actor_id' => $actorId ?? auth()->id(),
            'entity' => $entity,
            'entity_id' => $entityId,
            'action' => $action,
            'payload_json' => $payload,
        ]);
    }
}
