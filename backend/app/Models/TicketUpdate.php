<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TicketUpdate extends Model
{
    use HasFactory;

    protected $fillable = [
        'ticket_id',
        'actor_id',
        'type',
        'body',
        'meta_json',
    ];

    protected $casts = [
        'meta_json' => 'array',
    ];

    /**
     * Get the ticket that owns the update.
     */
    public function ticket(): BelongsTo
    {
        return $this->belongsTo(Ticket::class);
    }

    /**
     * Get the user who made the update.
     */
    public function actor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'actor_id');
    }

    /**
     * Scope a query to only include comments.
     */
    public function scopeComments($query)
    {
        return $query->where('type', 'comment');
    }

    /**
     * Scope a query to only include status changes.
     */
    public function scopeStatusChanges($query)
    {
        return $query->where('type', 'status_change');
    }
}
