<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Ticket extends Model
{
    use HasFactory;

    protected $fillable = [
        'number',
        'type',
        'subject',
        'description',
        'priority',
        'status',
        'requester_id',
        'assignee_id',
        'service_id',
        'sla_id',
        'due_at',
    ];

    protected $casts = [
        'due_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($ticket) {
            if (empty($ticket->number)) {
                $ticket->number = 'TKT-' . strtoupper(Str::random(8));
            }
        });
    }

    /**
     * Get the requester of the ticket.
     */
    public function requester(): BelongsTo
    {
        return $this->belongsTo(User::class, 'requester_id');
    }

    /**
     * Get the assignee of the ticket.
     */
    public function assignee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assignee_id');
    }

    /**
     * Get the service of the ticket.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Get the SLA of the ticket.
     */
    public function sla(): BelongsTo
    {
        return $this->belongsTo(Sla::class);
    }

    /**
     * Get the updates for the ticket.
     */
    public function updates(): HasMany
    {
        return $this->hasMany(TicketUpdate::class);
    }

    /**
     * Get the attachments for the ticket.
     */
    public function attachments(): HasMany
    {
        return $this->hasMany(Attachment::class);
    }

    /**
     * Scope a query to only include tickets with a specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include tickets with a specific priority.
     */
    public function scopeWithPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope a query to only include tickets assigned to a specific user.
     */
    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assignee_id', $userId);
    }

    /**
     * Scope a query to only include tickets created by a specific user.
     */
    public function scopeCreatedBy($query, $userId)
    {
        return $query->where('requester_id', $userId);
    }
}
