<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Sla extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'target_response_mins',
        'target_resolution_mins',
    ];

    /**
     * Get the tickets for this SLA.
     */
    public function tickets(): HasMany
    {
        return $this->hasMany(Ticket::class);
    }

    /**
     * Get target response time in hours.
     */
    public function getTargetResponseHoursAttribute(): float
    {
        return $this->target_response_mins / 60;
    }

    /**
     * Get target resolution time in hours.
     */
    public function getTargetResolutionHoursAttribute(): float
    {
        return $this->target_resolution_mins / 60;
    }
}
