<?php

namespace App\Listeners;

use App\Events\TicketAssigned;
use App\Mail\TicketAssigned as TicketAssignedMail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendTicketAssignedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(TicketAssigned $event): void
    {
        $ticket = $event->ticket->load(['requester', 'service']);
        $assignee = $event->assignee;

        // Send email to assignee
        Mail::to($assignee->email)
            ->send(new TicketAssignedMail($ticket, $assignee));
    }
}
