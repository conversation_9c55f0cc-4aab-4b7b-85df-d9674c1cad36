<?php

namespace App\Listeners;

use App\Events\TicketCreated;
use App\Mail\TicketCreated as TicketCreatedMail;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendTicketCreatedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(TicketCreated $event): void
    {
        $ticket = $event->ticket->load(['requester', 'service']);

        // Send email to requester
        Mail::to($ticket->requester->email)
            ->send(new TicketCreatedMail($ticket));

        // Send email to all agents and managers
        $agents = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['Agent', 'Manager', 'Admin']);
        })->get();

        foreach ($agents as $agent) {
            Mail::to($agent->email)
                ->send(new TicketCreatedMail($ticket));
        }
    }
}
