<?php

namespace App\Listeners;

use App\Events\TicketUpdated;
use App\Mail\TicketUpdated as TicketUpdatedMail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendTicketUpdatedNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(TicketUpdated $event): void
    {
        $ticket = $event->ticket->load(['requester', 'assignee']);
        $update = $event->update->load('actor');

        // Send email to requester
        Mail::to($ticket->requester->email)
            ->send(new TicketUpdatedMail($ticket, $update));

        // Send email to assignee if different from requester
        if ($ticket->assignee && $ticket->assignee->id !== $ticket->requester->id) {
            Mail::to($ticket->assignee->email)
                ->send(new TicketUpdatedMail($ticket, $update));
        }
    }
}
