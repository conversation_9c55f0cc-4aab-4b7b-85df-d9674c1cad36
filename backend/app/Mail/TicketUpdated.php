<?php

namespace App\Mail;

use App\Models\Ticket;
use App\Models\TicketUpdate;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class TicketUpdated extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $ticket;
    public $update;

    /**
     * Create a new message instance.
     */
    public function __construct(Ticket $ticket, TicketUpdate $update)
    {
        $this->ticket = $ticket;
        $this->update = $update;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Ticket Updated: ' . $this->ticket->subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.ticket-updated',
            with: [
                'ticket' => $this->ticket,
                'update' => $this->update,
                'actor' => $this->update->actor,
                'requester' => $this->ticket->requester,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
