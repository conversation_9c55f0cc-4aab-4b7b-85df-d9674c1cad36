<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Models\AuditLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

/**
 * @OA\Tag(
 *     name="Services",
 *     description="API Endpoints for service management"
 * )
 */
class ServiceController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/services",
     *     tags={"Services"},
     *     summary="Get services list",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Services list",
     *         @OA\JsonContent(
     *             @OA\Property(property="services", type="array", @OA\Items(type="object"))
     *         )
     *     )
     * )
     */
    public function index(): JsonResponse
    {
        $services = Service::with('owner')->get();

        return response()->json(['services' => $services]);
    }

    /**
     * @OA\Post(
     *     path="/api/services",
     *     tags={"Services"},
     *     summary="Create a new service",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"name","owner_id"},
     *             @OA\Property(property="name", type="string", example="Email Service"),
     *             @OA\Property(property="owner_id", type="integer", example=1)
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Service created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Service created successfully"),
     *             @OA\Property(property="service", type="object")
     *         )
     *     )
     * )
     */
    public function store(Request $request): JsonResponse
    {
        $user = auth()->user();

        if (!$user->hasPermission('manage_services')) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:services',
            'owner_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $service = Service::create([
            'name' => $request->name,
            'owner_id' => $request->owner_id,
        ]);

        // Log audit
        AuditLog::log('service', $service->id, 'created', [
            'name' => $service->name,
            'owner_id' => $service->owner_id,
        ]);

        return response()->json([
            'message' => 'Service created successfully',
            'service' => $service->load('owner'),
        ], 201);
    }

    /**
     * @OA\Get(
     *     path="/api/services/{id}",
     *     tags={"Services"},
     *     summary="Get service details",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Service details",
     *         @OA\JsonContent(
     *             @OA\Property(property="service", type="object")
     *         )
     *     )
     * )
     */
    public function show($id): JsonResponse
    {
        $service = Service::with(['owner', 'tickets'])->findOrFail($id);

        return response()->json(['service' => $service]);
    }

    /**
     * @OA\Put(
     *     path="/api/services/{id}",
     *     tags={"Services"},
     *     summary="Update service",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="name", type="string", example="Updated Email Service"),
     *             @OA\Property(property="owner_id", type="integer", example=2)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Service updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Service updated successfully"),
     *             @OA\Property(property="service", type="object")
     *         )
     *     )
     * )
     */
    public function update(Request $request, $id): JsonResponse
    {
        $user = auth()->user();

        if (!$user->hasPermission('manage_services')) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        $service = Service::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255|unique:services,name,' . $service->id,
            'owner_id' => 'sometimes|required|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $oldValues = $service->toArray();
        $service->update($request->only(['name', 'owner_id']));

        // Log audit
        AuditLog::log('service', $service->id, 'updated', [
            'old_values' => $oldValues,
            'new_values' => $service->fresh()->toArray(),
        ]);

        return response()->json([
            'message' => 'Service updated successfully',
            'service' => $service->load('owner'),
        ]);
    }

    /**
     * @OA\Delete(
     *     path="/api/services/{id}",
     *     tags={"Services"},
     *     summary="Delete service",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Service deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Service deleted successfully")
     *         )
     *     )
     * )
     */
    public function destroy($id): JsonResponse
    {
        $user = auth()->user();

        if (!$user->hasPermission('manage_services')) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        $service = Service::findOrFail($id);

        // Log audit before deletion
        AuditLog::log('service', $service->id, 'deleted', [
            'name' => $service->name,
            'owner_id' => $service->owner_id,
        ]);

        $service->delete();

        return response()->json(['message' => 'Service deleted successfully']);
    }
}
