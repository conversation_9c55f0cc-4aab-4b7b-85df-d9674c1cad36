<?php

namespace App\Http\Controllers;

/**
 * @OA\Info(
 *     title="ITSM Platform API",
 *     version="1.0.0",
 *     description="API documentation for ITSM Platform - A comprehensive IT Service Management system",
 *     @OA\Contact(
 *         email="<EMAIL>"
 *     ),
 *     @OA\License(
 *         name="MIT",
 *         url="https://opensource.org/licenses/MIT"
 *     )
 * )
 *
 * @OA\Server(
 *     url="http://localhost:8000",
 *     description="Local development server"
 * )
 *
 * @OA\SecurityScheme(
 *     securityScheme="bearerAuth",
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT",
 *     description="JWT Authorization header using the Bearer scheme. Example: 'Authorization: Bearer {token}'"
 * )
 *
 * @OA\Tag(
 *     name="Authentication",
 *     description="User authentication endpoints"
 * )
 *
 * @OA\Tag(
 *     name="Tickets",
 *     description="Ticket management endpoints"
 * )
 *
 * @OA\Tag(
 *     name="Users",
 *     description="User management endpoints"
 * )
 *
 * @OA\Tag(
 *     name="Services",
 *     description="Service management endpoints"
 * )
 *
 * @OA\Tag(
 *     name="SLAs",
 *     description="SLA management endpoints"
 * )
 *
 * @OA\Tag(
 *     name="Reports",
 *     description="Reports and analytics endpoints"
 * )
 */
abstract class Controller
{
    //
}
