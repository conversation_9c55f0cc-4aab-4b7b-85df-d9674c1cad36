<?php

namespace App\Http\Controllers;

use App\Models\Sla;
use App\Models\AuditLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

/**
 * @OA\Tag(
 *     name="SLAs",
 *     description="API Endpoints for SLA management"
 * )
 */
class SlaController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/slas",
     *     tags={"SLAs"},
     *     summary="Get SLAs list",
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="SLAs list",
     *         @OA\JsonContent(
     *             @OA\Property(property="slas", type="array", @OA\Items(type="object"))
     *         )
     *     )
     * )
     */
    public function index(): JsonResponse
    {
        $slas = Sla::all();

        return response()->json(['slas' => $slas]);
    }

    /**
     * @OA\Post(
     *     path="/api/slas",
     *     tags={"SLAs"},
     *     summary="Create a new SLA",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"name","target_response_mins","target_resolution_mins"},
     *             @OA\Property(property="name", type="string", example="Standard SLA"),
     *             @OA\Property(property="target_response_mins", type="integer", example=60),
     *             @OA\Property(property="target_resolution_mins", type="integer", example=480)
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="SLA created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="SLA created successfully"),
     *             @OA\Property(property="sla", type="object")
     *         )
     *     )
     * )
     */
    public function store(Request $request): JsonResponse
    {
        $user = auth()->user();

        if (!$user->hasPermission('manage_sla')) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:slas',
            'target_response_mins' => 'required|integer|min:1',
            'target_resolution_mins' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $sla = Sla::create([
            'name' => $request->name,
            'target_response_mins' => $request->target_response_mins,
            'target_resolution_mins' => $request->target_resolution_mins,
        ]);

        // Log audit
        AuditLog::log('sla', $sla->id, 'created', [
            'name' => $sla->name,
            'target_response_mins' => $sla->target_response_mins,
            'target_resolution_mins' => $sla->target_resolution_mins,
        ]);

        return response()->json([
            'message' => 'SLA created successfully',
            'sla' => $sla,
        ], 201);
    }

    /**
     * @OA\Get(
     *     path="/api/slas/{id}",
     *     tags={"SLAs"},
     *     summary="Get SLA details",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="SLA details",
     *         @OA\JsonContent(
     *             @OA\Property(property="sla", type="object")
     *         )
     *     )
     * )
     */
    public function show($id): JsonResponse
    {
        $sla = Sla::with('tickets')->findOrFail($id);

        return response()->json(['sla' => $sla]);
    }

    /**
     * @OA\Put(
     *     path="/api/slas/{id}",
     *     tags={"SLAs"},
     *     summary="Update SLA",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="name", type="string", example="Updated Standard SLA"),
     *             @OA\Property(property="target_response_mins", type="integer", example=30),
     *             @OA\Property(property="target_resolution_mins", type="integer", example=240)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="SLA updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="SLA updated successfully"),
     *             @OA\Property(property="sla", type="object")
     *         )
     *     )
     * )
     */
    public function update(Request $request, $id): JsonResponse
    {
        $user = auth()->user();

        if (!$user->hasPermission('manage_sla')) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        $sla = Sla::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255|unique:slas,name,' . $sla->id,
            'target_response_mins' => 'sometimes|required|integer|min:1',
            'target_resolution_mins' => 'sometimes|required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $oldValues = $sla->toArray();
        $sla->update($request->only(['name', 'target_response_mins', 'target_resolution_mins']));

        // Log audit
        AuditLog::log('sla', $sla->id, 'updated', [
            'old_values' => $oldValues,
            'new_values' => $sla->fresh()->toArray(),
        ]);

        return response()->json([
            'message' => 'SLA updated successfully',
            'sla' => $sla,
        ]);
    }

    /**
     * @OA\Delete(
     *     path="/api/slas/{id}",
     *     tags={"SLAs"},
     *     summary="Delete SLA",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="SLA deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="SLA deleted successfully")
     *         )
     *     )
     * )
     */
    public function destroy($id): JsonResponse
    {
        $user = auth()->user();

        if (!$user->hasPermission('manage_sla')) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        $sla = Sla::findOrFail($id);

        // Log audit before deletion
        AuditLog::log('sla', $sla->id, 'deleted', [
            'name' => $sla->name,
            'target_response_mins' => $sla->target_response_mins,
            'target_resolution_mins' => $sla->target_resolution_mins,
        ]);

        $sla->delete();

        return response()->json(['message' => 'SLA deleted successfully']);
    }
}
