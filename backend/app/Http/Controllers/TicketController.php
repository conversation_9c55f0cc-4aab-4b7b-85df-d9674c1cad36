<?php

namespace App\Http\Controllers;

use App\Models\Ticket;
use App\Models\TicketUpdate;
use App\Models\Attachment;
use App\Models\AuditLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

/**
 * @OA\Tag(
 *     name="Tickets",
 *     description="API Endpoints for ticket management"
 * )
 */
class TicketController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/tickets",
     *     tags={"Tickets"},
     *     summary="Get tickets list",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by status",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="priority",
     *         in="query",
     *         description="Filter by priority",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="assignee_id",
     *         in="query",
     *         description="Filter by assignee",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Tickets list",
     *         @OA\JsonContent(
     *             @OA\Property(property="tickets", type="array", @OA\Items(type="object"))
     *         )
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        $user = auth()->user();
        $query = Ticket::with(['requester', 'assignee', 'service', 'sla']);

        // Apply role-based filtering
        if ($user->hasRole('End User')) {
            $query->where('requester_id', $user->id);
        } elseif ($user->hasRole('Agent')) {
            $query->where('assignee_id', $user->id);
        }
        // Managers and Admins can see all tickets

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->has('assignee_id')) {
            $query->where('assignee_id', $request->assignee_id);
        }

        $tickets = $query->orderBy('created_at', 'desc')->paginate(20);

        return response()->json(['tickets' => $tickets]);
    }

    /**
     * @OA\Post(
     *     path="/api/tickets",
     *     tags={"Tickets"},
     *     summary="Create a new ticket",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"subject","description","type"},
     *             @OA\Property(property="subject", type="string", example="Login issue"),
     *             @OA\Property(property="description", type="string", example="Cannot login to the system"),
     *             @OA\Property(property="type", type="string", example="Incident"),
     *             @OA\Property(property="priority", type="string", example="Medium"),
     *             @OA\Property(property="service_id", type="integer", example=1)
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Ticket created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Ticket created successfully"),
     *             @OA\Property(property="ticket", type="object")
     *         )
     *     )
     * )
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'subject' => 'required|string|max:255',
            'description' => 'required|string',
            'type' => 'required|string|max:100',
            'priority' => 'nullable|in:Low,Medium,High,Critical',
            'service_id' => 'nullable|exists:services,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $ticket = Ticket::create([
            'subject' => $request->subject,
            'description' => $request->description,
            'type' => $request->type,
            'priority' => $request->priority ?? 'Medium',
            'status' => 'New',
            'requester_id' => auth()->id(),
            'service_id' => $request->service_id,
        ]);

        // Log audit
        AuditLog::log('ticket', $ticket->id, 'created', [
            'subject' => $ticket->subject,
            'type' => $ticket->type,
            'priority' => $ticket->priority,
        ]);

        return response()->json([
            'message' => 'Ticket created successfully',
            'ticket' => $ticket->load(['requester', 'service']),
        ], 201);
    }

    /**
     * @OA\Get(
     *     path="/api/tickets/{id}",
     *     tags={"Tickets"},
     *     summary="Get ticket details",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Ticket details",
     *         @OA\JsonContent(
     *             @OA\Property(property="ticket", type="object")
     *         )
     *     )
     * )
     */
    public function show($id): JsonResponse
    {
        $user = auth()->user();
        $ticket = Ticket::with(['requester', 'assignee', 'service', 'sla', 'updates.actor', 'attachments'])->findOrFail($id);

        // Check permissions
        if ($user->hasRole('End User') && $ticket->requester_id !== $user->id) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        if ($user->hasRole('Agent') && $ticket->assignee_id !== $user->id) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        return response()->json(['ticket' => $ticket]);
    }

    /**
     * @OA\Patch(
     *     path="/api/tickets/{id}",
     *     tags={"Tickets"},
     *     summary="Update ticket",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="string", example="In Progress"),
     *             @OA\Property(property="priority", type="string", example="High"),
     *             @OA\Property(property="assignee_id", type="integer", example=2)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Ticket updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Ticket updated successfully"),
     *             @OA\Property(property="ticket", type="object")
     *         )
     *     )
     * )
     */
    public function update(Request $request, $id): JsonResponse
    {
        $user = auth()->user();
        $ticket = Ticket::findOrFail($id);

        // Check permissions
        if ($user->hasRole('End User')) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        if ($user->hasRole('Agent') && $ticket->assignee_id !== $user->id) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        $validator = Validator::make($request->all(), [
            'status' => 'nullable|in:New,In Progress,Pending,Resolved,Closed',
            'priority' => 'nullable|in:Low,Medium,High,Critical',
            'assignee_id' => 'nullable|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $changes = [];
        $oldValues = [];

        if ($request->has('status') && $request->status !== $ticket->status) {
            $oldValues['status'] = $ticket->status;
            $ticket->status = $request->status;
            $changes['status'] = $request->status;
        }

        if ($request->has('priority') && $request->priority !== $ticket->priority) {
            $oldValues['priority'] = $ticket->priority;
            $ticket->priority = $request->priority;
            $changes['priority'] = $request->priority;
        }

        if ($request->has('assignee_id') && $request->assignee_id !== $ticket->assignee_id) {
            $oldValues['assignee_id'] = $ticket->assignee_id;
            $ticket->assignee_id = $request->assignee_id;
            $changes['assignee_id'] = $request->assignee_id;
        }

        if (!empty($changes)) {
            $ticket->save();

            // Create ticket update entries
            foreach ($changes as $field => $newValue) {
                TicketUpdate::create([
                    'ticket_id' => $ticket->id,
                    'actor_id' => $user->id,
                    'type' => $field === 'assignee_id' ? 'assignment' : $field . '_change',
                    'meta_json' => [
                        'field' => $field,
                        'old_value' => $oldValues[$field],
                        'new_value' => $newValue,
                    ],
                ]);
            }

            // Log audit
            AuditLog::log('ticket', $ticket->id, 'updated', $changes);
        }

        return response()->json([
            'message' => 'Ticket updated successfully',
            'ticket' => $ticket->load(['requester', 'assignee', 'service', 'sla']),
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/tickets/{id}/comment",
     *     tags={"Tickets"},
     *     summary="Add comment to ticket",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"body"},
     *             @OA\Property(property="body", type="string", example="This is a comment")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Comment added successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Comment added successfully"),
     *             @OA\Property(property="comment", type="object")
     *         )
     *     )
     * )
     */
    public function addComment(Request $request, $id): JsonResponse
    {
        $user = auth()->user();
        $ticket = Ticket::findOrFail($id);

        // Check permissions
        if ($user->hasRole('End User') && $ticket->requester_id !== $user->id) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        if ($user->hasRole('Agent') && $ticket->assignee_id !== $user->id) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        $validator = Validator::make($request->all(), [
            'body' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $comment = TicketUpdate::create([
            'ticket_id' => $ticket->id,
            'actor_id' => $user->id,
            'type' => 'comment',
            'body' => $request->body,
        ]);

        // Log audit
        AuditLog::log('ticket', $ticket->id, 'comment_added', [
            'comment_id' => $comment->id,
        ]);

        return response()->json([
            'message' => 'Comment added successfully',
            'comment' => $comment->load('actor'),
        ], 201);
    }

    /**
     * @OA\Post(
     *     path="/api/tickets/{id}/attachment",
     *     tags={"Tickets"},
     *     summary="Add attachment to ticket",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 @OA\Property(
     *                     property="file",
     *                     type="string",
     *                     format="binary"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Attachment uploaded successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Attachment uploaded successfully"),
     *             @OA\Property(property="attachment", type="object")
     *         )
     *     )
     * )
     */
    public function addAttachment(Request $request, $id): JsonResponse
    {
        $user = auth()->user();
        $ticket = Ticket::findOrFail($id);

        // Check permissions
        if ($user->hasRole('End User') && $ticket->requester_id !== $user->id) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        if ($user->hasRole('Agent') && $ticket->assignee_id !== $user->id) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        $validator = Validator::make($request->all(), [
            'file' => 'required|file|max:10240', // 10MB max
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $file = $request->file('file');
        $path = $file->store('attachments', 'public');

        $attachment = Attachment::create([
            'ticket_id' => $ticket->id,
            'path' => $path,
            'mime' => $file->getMimeType(),
            'size' => $file->getSize(),
            'uploader_id' => $user->id,
        ]);

        // Log audit
        AuditLog::log('ticket', $ticket->id, 'attachment_added', [
            'attachment_id' => $attachment->id,
            'filename' => $file->getClientOriginalName(),
        ]);

        return response()->json([
            'message' => 'Attachment uploaded successfully',
            'attachment' => $attachment,
        ], 201);
    }
}
