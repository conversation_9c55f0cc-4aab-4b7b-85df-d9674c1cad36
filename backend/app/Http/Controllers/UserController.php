<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Role;
use App\Models\AuditLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

/**
 * @OA\Tag(
 *     name="Users",
 *     description="API Endpoints for user management"
 * )
 */
class UserController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/users",
     *     tags={"Users"},
     *     summary="Get users list",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="role",
     *         in="query",
     *         description="Filter by role",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Users list",
     *         @OA\JsonContent(
     *             @OA\Property(property="users", type="array", @OA\Items(type="object"))
     *         )
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        $user = auth()->user();

        if (!$user->hasPermission('manage_users')) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        $query = User::with('roles');

        if ($request->has('role')) {
            $query->whereHas('roles', function ($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        $users = $query->paginate(20);

        return response()->json(['users' => $users]);
    }

    /**
     * @OA\Post(
     *     path="/api/users",
     *     tags={"Users"},
     *     summary="Create a new user",
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"name","email","password","role_id"},
     *             @OA\Property(property="name", type="string", example="John Doe"),
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *             @OA\Property(property="password", type="string", format="password", example="password123"),
     *             @OA\Property(property="phone", type="string", example="+1234567890"),
     *             @OA\Property(property="role_id", type="integer", example=1),
     *             @OA\Property(property="status", type="string", example="active")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="User created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="User created successfully"),
     *             @OA\Property(property="user", type="object")
     *         )
     *     )
     * )
     */
    public function store(Request $request): JsonResponse
    {
        $currentUser = auth()->user();

        if (!$currentUser->hasPermission('manage_users')) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:6',
            'phone' => 'nullable|string|max:20',
            'role_id' => 'required|exists:roles,id',
            'status' => 'nullable|in:active,inactive',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'phone' => $request->phone,
            'status' => $request->status ?? 'active',
        ]);

        // Assign role
        $user->roles()->attach($request->role_id);

        // Log audit
        AuditLog::log('user', $user->id, 'created', [
            'name' => $user->name,
            'email' => $user->email,
            'role_id' => $request->role_id,
        ]);

        return response()->json([
            'message' => 'User created successfully',
            'user' => $user->load('roles'),
        ], 201);
    }

    /**
     * @OA\Get(
     *     path="/api/users/{id}",
     *     tags={"Users"},
     *     summary="Get user details",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="User details",
     *         @OA\JsonContent(
     *             @OA\Property(property="user", type="object")
     *         )
     *     )
     * )
     */
    public function show($id): JsonResponse
    {
        $currentUser = auth()->user();

        if (!$currentUser->hasPermission('manage_users') && $currentUser->id != $id) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        $user = User::with(['roles', 'createdTickets', 'assignedTickets'])->findOrFail($id);

        return response()->json(['user' => $user]);
    }

    /**
     * @OA\Patch(
     *     path="/api/users/{id}",
     *     tags={"Users"},
     *     summary="Update user",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="name", type="string", example="John Doe Updated"),
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *             @OA\Property(property="phone", type="string", example="+1234567891"),
     *             @OA\Property(property="role_id", type="integer", example=2),
     *             @OA\Property(property="status", type="string", example="inactive")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="User updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="User updated successfully"),
     *             @OA\Property(property="user", type="object")
     *         )
     *     )
     * )
     */
    public function update(Request $request, $id): JsonResponse
    {
        $currentUser = auth()->user();
        $user = User::findOrFail($id);

        // Check permissions
        if (!$currentUser->hasPermission('manage_users') && $currentUser->id != $id) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        // Non-admin users can only update their own basic info
        $allowedFields = ['name', 'phone'];
        if ($currentUser->hasPermission('manage_users')) {
            $allowedFields = ['name', 'email', 'phone', 'role_id', 'status'];
        }

        $rules = [];
        if (in_array('name', $allowedFields)) {
            $rules['name'] = 'sometimes|required|string|max:255';
        }
        if (in_array('email', $allowedFields)) {
            $rules['email'] = 'sometimes|required|string|email|max:255|unique:users,email,' . $user->id;
        }
        if (in_array('phone', $allowedFields)) {
            $rules['phone'] = 'nullable|string|max:20';
        }
        if (in_array('role_id', $allowedFields)) {
            $rules['role_id'] = 'sometimes|required|exists:roles,id';
        }
        if (in_array('status', $allowedFields)) {
            $rules['status'] = 'sometimes|required|in:active,inactive';
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $oldValues = $user->toArray();
        $user->update($request->only($allowedFields));

        // Update role if provided and allowed
        if ($request->has('role_id') && in_array('role_id', $allowedFields)) {
            $user->roles()->sync([$request->role_id]);
        }

        // Log audit
        AuditLog::log('user', $user->id, 'updated', [
            'old_values' => $oldValues,
            'new_values' => $user->fresh()->toArray(),
        ]);

        return response()->json([
            'message' => 'User updated successfully',
            'user' => $user->load('roles'),
        ]);
    }

    /**
     * @OA\Delete(
     *     path="/api/users/{id}",
     *     tags={"Users"},
     *     summary="Delete user",
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="User deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="User deleted successfully")
     *         )
     *     )
     * )
     */
    public function destroy($id): JsonResponse
    {
        $currentUser = auth()->user();

        if (!$currentUser->hasPermission('manage_users')) {
            return response()->json(['error' => 'Forbidden'], 403);
        }

        $user = User::findOrFail($id);

        // Prevent deleting self
        if ($currentUser->id == $user->id) {
            return response()->json(['error' => 'Cannot delete yourself'], 422);
        }

        // Log audit before deletion
        AuditLog::log('user', $user->id, 'deleted', [
            'name' => $user->name,
            'email' => $user->email,
        ]);

        $user->delete();

        return response()->json(['message' => 'User deleted successfully']);
    }
}
