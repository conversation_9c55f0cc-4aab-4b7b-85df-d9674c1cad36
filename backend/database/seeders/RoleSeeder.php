<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'name' => 'End User',
                'permissions_json' => ['create_ticket', 'view_own_tickets']
            ],
            [
                'name' => 'Agent',
                'permissions_json' => ['view_assigned_tickets', 'update_ticket_status', 'add_comments']
            ],
            [
                'name' => 'Manager',
                'permissions_json' => ['view_reports', 'assign_tickets', 'view_assigned_tickets', 'update_ticket_status', 'add_comments']
            ],
            [
                'name' => 'Admin',
                'permissions_json' => ['full_access', 'manage_users', 'manage_services', 'manage_sla', 'view_reports', 'assign_tickets', 'view_assigned_tickets', 'update_ticket_status', 'add_comments', 'create_ticket', 'view_own_tickets']
            ]
        ];

        foreach ($roles as $roleData) {
            Role::updateOrCreate(
                ['name' => $roleData['name']],
                ['permissions_json' => $roleData['permissions_json']]
            );
        }
    }
}
