<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Role;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => Hash::make('admin123'),
                'phone' => '+1234567890',
                'status' => 'active',
                'email_verified_at' => now(),
            ]
        );

        // Assign Admin role
        $adminRole = Role::where('name', 'Admin')->first();
        if ($adminRole && !$admin->hasRole('Admin')) {
            $admin->roles()->attach($adminRole->id);
        }

        // Create sample agent user
        $agent = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Support Agent',
                'password' => Hash::make('agent123'),
                'phone' => '+1234567891',
                'status' => 'active',
                'email_verified_at' => now(),
            ]
        );

        // Assign Agent role
        $agentRole = Role::where('name', 'Agent')->first();
        if ($agentRole && !$agent->hasRole('Agent')) {
            $agent->roles()->attach($agentRole->id);
        }

        // Create sample manager user
        $manager = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Support Manager',
                'password' => Hash::make('manager123'),
                'phone' => '+1234567892',
                'status' => 'active',
                'email_verified_at' => now(),
            ]
        );

        // Assign Manager role
        $managerRole = Role::where('name', 'Manager')->first();
        if ($managerRole && !$manager->hasRole('Manager')) {
            $manager->roles()->attach($managerRole->id);
        }

        // Create sample end user
        $endUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'End User',
                'password' => Hash::make('user123'),
                'phone' => '+1234567893',
                'status' => 'active',
                'email_verified_at' => now(),
            ]
        );

        // Assign End User role
        $endUserRole = Role::where('name', 'End User')->first();
        if ($endUserRole && !$endUser->hasRole('End User')) {
            $endUser->roles()->attach($endUserRole->id);
        }
    }
}
