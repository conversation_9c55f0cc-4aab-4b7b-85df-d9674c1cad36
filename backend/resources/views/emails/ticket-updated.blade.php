<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Ticket Updated</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .ticket-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .update-info {
            background-color: #d1ecf1;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>Ticket Updated</h2>
    </div>

    <p>Hello,</p>

    <p>Ticket {{ $ticket->number }} has been updated:</p>

    <div class="ticket-info">
        <strong>Ticket Number:</strong> {{ $ticket->number }}<br>
        <strong>Subject:</strong> {{ $ticket->subject }}<br>
        <strong>Current Status:</strong> {{ $ticket->status }}<br>
        <strong>Priority:</strong> {{ $ticket->priority }}<br>
        <strong>Requester:</strong> {{ $requester->name }} ({{ $requester->email }})
    </div>

    <div class="update-info">
        <strong>Update Type:</strong> {{ ucfirst(str_replace('_', ' ', $update->type)) }}<br>
        <strong>Updated By:</strong> {{ $actor->name }}<br>
        <strong>Updated At:</strong> {{ $update->created_at->format('Y-m-d H:i:s') }}<br>
        
        @if($update->body)
        <strong>Comment:</strong><br>
        {{ $update->body }}
        @endif

        @if($update->meta_json)
        <strong>Changes:</strong><br>
        @foreach($update->meta_json as $key => $value)
            @if($key === 'old_value' || $key === 'new_value')
                {{ ucfirst(str_replace('_', ' ', $key)) }}: {{ $value }}<br>
            @endif
        @endforeach
        @endif
    </div>

    <p>You can view the full ticket details by logging into the ITSM system.</p>

    <div class="footer">
        <p>This is an automated message from the ITSM system. Please do not reply to this email.</p>
    </div>
</body>
</html>
