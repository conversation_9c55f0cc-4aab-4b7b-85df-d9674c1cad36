<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>New Ticket Created</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .ticket-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>New Ticket Created</h2>
    </div>

    <p>Hello,</p>

    <p>A new ticket has been created in the ITSM system:</p>

    <div class="ticket-info">
        <strong>Ticket Number:</strong> {{ $ticket->number }}<br>
        <strong>Subject:</strong> {{ $ticket->subject }}<br>
        <strong>Type:</strong> {{ $ticket->type }}<br>
        <strong>Priority:</strong> {{ $ticket->priority }}<br>
        <strong>Status:</strong> {{ $ticket->status }}<br>
        <strong>Requester:</strong> {{ $requester->name }} ({{ $requester->email }})<br>
        @if($service)
        <strong>Service:</strong> {{ $service->name }}<br>
        @endif
        <strong>Created:</strong> {{ $ticket->created_at->format('Y-m-d H:i:s') }}
    </div>

    <p><strong>Description:</strong></p>
    <p>{{ $ticket->description }}</p>

    <p>You can view and manage this ticket by logging into the ITSM system.</p>

    <div class="footer">
        <p>This is an automated message from the ITSM system. Please do not reply to this email.</p>
    </div>
</body>
</html>
