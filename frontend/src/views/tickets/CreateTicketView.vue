<template>
  <AppLayout>
    <div class="px-4 py-6 sm:px-0">
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">Create New Ticket</h1>
        <p class="mt-1 text-sm text-gray-600">
          Submit a new support request
        </p>
      </div>

      <div class="max-w-2xl">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <div class="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
            <div class="md:grid md:grid-cols-3 md:gap-6">
              <div class="md:col-span-1">
                <h3 class="text-lg font-medium leading-6 text-gray-900">Ticket Information</h3>
                <p class="mt-1 text-sm text-gray-500">
                  Provide details about your request
                </p>
              </div>
              <div class="mt-5 md:mt-0 md:col-span-2">
                <div class="grid grid-cols-6 gap-6">
                  <div class="col-span-6">
                    <label for="subject" class="form-label">Subject *</label>
                    <input
                      id="subject"
                      v-model="form.subject"
                      type="text"
                      required
                      class="form-input"
                      placeholder="Brief description of the issue"
                    />
                  </div>

                  <div class="col-span-6 sm:col-span-3">
                    <label for="type" class="form-label">Type *</label>
                    <select id="type" v-model="form.type" required class="form-input">
                      <option value="">Select type</option>
                      <option value="Incident">Incident</option>
                      <option value="Request">Request</option>
                      <option value="Change">Change</option>
                      <option value="Problem">Problem</option>
                    </select>
                  </div>

                  <div class="col-span-6 sm:col-span-3">
                    <label for="priority" class="form-label">Priority</label>
                    <select id="priority" v-model="form.priority" class="form-input">
                      <option value="Low">Low</option>
                      <option value="Medium">Medium</option>
                      <option value="High">High</option>
                      <option value="Critical">Critical</option>
                    </select>
                  </div>

                  <div class="col-span-6" v-if="services.length > 0">
                    <label for="service_id" class="form-label">Service</label>
                    <select id="service_id" v-model="form.service_id" class="form-input">
                      <option value="">Select service</option>
                      <option v-for="service in services" :key="service.id" :value="service.id">
                        {{ service.name }}
                      </option>
                    </select>
                  </div>

                  <div class="col-span-6">
                    <label for="description" class="form-label">Description *</label>
                    <textarea
                      id="description"
                      v-model="form.description"
                      rows="4"
                      required
                      class="form-input"
                      placeholder="Detailed description of the issue or request"
                    ></textarea>
                  </div>

                  <div class="col-span-6">
                    <label for="attachment" class="form-label">Attachment</label>
                    <input
                      id="attachment"
                      ref="fileInput"
                      type="file"
                      @change="handleFileSelect"
                      class="form-input"
                      accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt"
                    />
                    <p class="mt-1 text-sm text-gray-500">
                      Supported formats: JPG, PNG, GIF, PDF, DOC, DOCX, TXT (Max 10MB)
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="errors" class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="text-red-600 text-sm">
              <div v-for="(error, field) in errors" :key="field">
                <div v-for="message in error" :key="message">
                  {{ message }}
                </div>
              </div>
            </div>
          </div>

          <div class="flex justify-end space-x-3">
            <router-link to="/tickets" class="btn btn-secondary">
              Cancel
            </router-link>
            <button
              type="submit"
              :disabled="loading"
              class="btn btn-primary"
            >
              <span v-if="loading">Creating...</span>
              <span v-else>Create Ticket</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import AppLayout from '@/components/AppLayout.vue'
import { useTicketsStore } from '@/stores/tickets'
import api from '@/services/api'

const router = useRouter()
const ticketsStore = useTicketsStore()

const form = ref({
  subject: '',
  type: '',
  priority: 'Medium',
  service_id: '',
  description: ''
})

const selectedFile = ref(null)
const services = ref([])
const loading = ref(false)
const errors = ref(null)
const fileInput = ref(null)

const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    if (file.size > 10 * 1024 * 1024) { // 10MB
      alert('File size must be less than 10MB')
      fileInput.value.value = ''
      return
    }
    selectedFile.value = file
  }
}

const handleSubmit = async () => {
  loading.value = true
  errors.value = null

  try {
    // Create ticket
    const result = await ticketsStore.createTicket(form.value)

    if (result.success) {
      // Upload attachment if selected
      if (selectedFile.value) {
        await ticketsStore.addAttachment(result.ticket.id, selectedFile.value)
      }

      router.push(`/tickets/${result.ticket.id}`)
    } else {
      errors.value = result.error
    }
  } catch (error) {
    errors.value = { general: ['An unexpected error occurred'] }
  } finally {
    loading.value = false
  }
}

const fetchServices = async () => {
  try {
    const response = await api.get('/services')
    services.value = response.data.services
  } catch (error) {
    console.error('Failed to fetch services:', error)
  }
}

onMounted(() => {
  fetchServices()
})
</script>
