<template>
  <AppLayout>
    <div class="px-4 py-6 sm:px-0">
      <div v-if="loading" class="text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
      </div>

      <div v-else-if="ticket" class="space-y-6">
        <!-- Header -->
        <div class="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
          <div class="md:flex md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
              <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                {{ ticket.number }}
              </h2>
              <div class="mt-1 flex flex-col sm:flex-row sm:flex-wrap sm:mt-0 sm:space-x-6">
                <div class="mt-2 flex items-center text-sm text-gray-500">
                  <span :class="getStatusBadgeClass(ticket.status)" class="badge">
                    {{ ticket.status }}
                  </span>
                </div>
                <div class="mt-2 flex items-center text-sm text-gray-500">
                  <span :class="getPriorityBadgeClass(ticket.priority)" class="badge">
                    {{ ticket.priority }}
                  </span>
                </div>
                <div class="mt-2 flex items-center text-sm text-gray-500">
                  Created {{ formatDate(ticket.created_at) }}
                </div>
              </div>
            </div>
            <div class="mt-4 flex md:mt-0 md:ml-4">
              <router-link to="/tickets" class="btn btn-secondary mr-3">
                Back to Tickets
              </router-link>
              <button
                v-if="canUpdateTicket"
                @click="showUpdateModal = true"
                class="btn btn-primary"
              >
                Update Ticket
              </button>
            </div>
          </div>
        </div>

        <!-- Ticket Details -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
          <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              {{ ticket.subject }}
            </h3>
          </div>
          <div class="border-t border-gray-200 px-4 py-5 sm:p-0">
            <dl class="sm:divide-y sm:divide-gray-200">
              <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Type</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ ticket.type }}</dd>
              </div>
              <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Requester</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  {{ ticket.requester?.name }} ({{ ticket.requester?.email }})
                </dd>
              </div>
              <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium text-gray-500">Description</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                  <div class="whitespace-pre-wrap">{{ ticket.description }}</div>
                </dd>
              </div>
            </dl>
          </div>
        </div>

        <!-- Comments Section -->
        <div class="bg-white shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
              Activity Timeline
            </h3>
            
            <!-- Add Comment Form -->
            <div v-if="canComment" class="mb-6">
              <form @submit.prevent="addComment" class="space-y-3">
                <textarea
                  v-model="newComment"
                  rows="3"
                  class="form-input"
                  placeholder="Add a comment..."
                  required
                ></textarea>
                <div class="flex justify-end">
                  <button
                    type="submit"
                    :disabled="commentLoading"
                    class="btn btn-primary"
                  >
                    <span v-if="commentLoading">Adding...</span>
                    <span v-else>Add Comment</span>
                  </button>
                </div>
              </form>
            </div>

            <!-- Timeline -->
            <div class="flow-root">
              <ul class="-mb-8">
                <li v-for="(update, index) in ticket.updates" :key="update.id">
                  <div class="relative pb-8">
                    <span
                      v-if="index !== ticket.updates.length - 1"
                      class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                      aria-hidden="true"
                    ></span>
                    <div class="relative flex space-x-3">
                      <div>
                        <span class="h-8 w-8 rounded-full bg-gray-400 flex items-center justify-center ring-8 ring-white">
                          <span class="text-xs font-medium text-white">
                            {{ update.actor?.name?.charAt(0).toUpperCase() }}
                          </span>
                        </span>
                      </div>
                      <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                        <div>
                          <p class="text-sm text-gray-500">
                            <span class="font-medium text-gray-900">{{ update.actor?.name }}</span>
                            {{ getUpdateTypeText(update.type) }}
                            <time class="text-gray-500">{{ formatDateTime(update.created_at) }}</time>
                          </p>
                          <div v-if="update.body" class="mt-2 text-sm text-gray-700">
                            <div class="whitespace-pre-wrap">{{ update.body }}</div>
                          </div>
                          <div v-if="update.meta_json" class="mt-2 text-sm text-gray-500">
                            <div v-for="(value, key) in update.meta_json" :key="key">
                              <span v-if="key === 'old_value'">From: {{ value }}</span>
                              <span v-if="key === 'new_value'">To: {{ value }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Update Modal -->
    <div v-if="showUpdateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Update Ticket</h3>
          <form @submit.prevent="updateTicket" class="space-y-4">
            <div>
              <label class="form-label">Status</label>
              <select v-model="updateForm.status" class="form-input">
                <option value="New">New</option>
                <option value="In Progress">In Progress</option>
                <option value="Pending">Pending</option>
                <option value="Resolved">Resolved</option>
                <option value="Closed">Closed</option>
              </select>
            </div>
            <div>
              <label class="form-label">Priority</label>
              <select v-model="updateForm.priority" class="form-input">
                <option value="Low">Low</option>
                <option value="Medium">Medium</option>
                <option value="High">High</option>
                <option value="Critical">Critical</option>
              </select>
            </div>
            <div class="flex justify-end space-x-3">
              <button
                type="button"
                @click="showUpdateModal = false"
                class="btn btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                :disabled="updateLoading"
                class="btn btn-primary"
              >
                <span v-if="updateLoading">Updating...</span>
                <span v-else>Update</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import AppLayout from '@/components/AppLayout.vue'
import { useAuthStore } from '@/stores/auth'
import { useTicketsStore } from '@/stores/tickets'

const route = useRoute()
const authStore = useAuthStore()
const ticketsStore = useTicketsStore()

const ticket = computed(() => ticketsStore.currentTicket)
const loading = computed(() => ticketsStore.loading)

const showUpdateModal = ref(false)
const updateForm = ref({
  status: '',
  priority: ''
})
const updateLoading = ref(false)

const newComment = ref('')
const commentLoading = ref(false)

const canUpdateTicket = computed(() => {
  if (!ticket.value) return false
  
  if (authStore.hasAnyRole(['Manager', 'Admin'])) return true
  if (authStore.hasRole('Agent') && ticket.value.assignee_id === authStore.user?.id) return true
  
  return false
})

const canComment = computed(() => {
  if (!ticket.value) return false
  
  if (authStore.hasRole('End User') && ticket.value.requester_id === authStore.user?.id) return true
  if (authStore.hasRole('Agent') && ticket.value.assignee_id === authStore.user?.id) return true
  if (authStore.hasAnyRole(['Manager', 'Admin'])) return true
  
  return false
})

const getStatusBadgeClass = (status) => {
  const classes = {
    'New': 'badge-info',
    'In Progress': 'badge-warning',
    'Pending': 'badge-warning',
    'Resolved': 'badge-success',
    'Closed': 'badge-secondary'
  }
  return classes[status] || 'badge-secondary'
}

const getPriorityBadgeClass = (priority) => {
  const classes = {
    'Low': 'badge-secondary',
    'Medium': 'badge-info',
    'High': 'badge-warning',
    'Critical': 'badge-danger'
  }
  return classes[priority] || 'badge-secondary'
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}

const formatDateTime = (date) => {
  return new Date(date).toLocaleString()
}

const getUpdateTypeText = (type) => {
  const texts = {
    'comment': 'commented',
    'status_change': 'changed status',
    'priority_change': 'changed priority',
    'assignment': 'assigned ticket'
  }
  return texts[type] || 'updated'
}

const addComment = async () => {
  if (!newComment.value.trim()) return
  
  commentLoading.value = true
  const result = await ticketsStore.addComment(ticket.value.id, newComment.value)
  
  if (result.success) {
    newComment.value = ''
  }
  
  commentLoading.value = false
}

const updateTicket = async () => {
  updateLoading.value = true
  
  const updates = {}
  if (updateForm.value.status !== ticket.value.status) {
    updates.status = updateForm.value.status
  }
  if (updateForm.value.priority !== ticket.value.priority) {
    updates.priority = updateForm.value.priority
  }
  
  if (Object.keys(updates).length > 0) {
    await ticketsStore.updateTicket(ticket.value.id, updates)
  }
  
  showUpdateModal.value = false
  updateLoading.value = false
}

onMounted(async () => {
  const ticketId = route.params.id
  await ticketsStore.fetchTicket(ticketId)
  
  if (ticket.value) {
    updateForm.value = {
      status: ticket.value.status,
      priority: ticket.value.priority
    }
  }
})
</script>
