<template>
  <AppLayout>
    <div class="px-4 py-6 sm:px-0">
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-2xl font-bold text-gray-900">Tickets</h1>
          <p class="mt-2 text-sm text-gray-700">
            A list of all tickets in your account.
          </p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <router-link
            v-if="authStore.hasPermission('create_ticket')"
            to="/tickets/create"
            class="btn btn-primary"
          >
            Create Ticket
          </router-link>
        </div>
      </div>

      <!-- Filters -->
      <div class="mt-6 bg-white p-4 rounded-lg shadow">
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-4">
          <div>
            <label class="form-label">Status</label>
            <select v-model="filters.status" @change="applyFilters" class="form-input">
              <option value="">All Statuses</option>
              <option value="New">New</option>
              <option value="In Progress">In Progress</option>
              <option value="Pending">Pending</option>
              <option value="Resolved">Resolved</option>
              <option value="Closed">Closed</option>
            </select>
          </div>
          <div>
            <label class="form-label">Priority</label>
            <select v-model="filters.priority" @change="applyFilters" class="form-input">
              <option value="">All Priorities</option>
              <option value="Low">Low</option>
              <option value="Medium">Medium</option>
              <option value="High">High</option>
              <option value="Critical">Critical</option>
            </select>
          </div>
          <div v-if="authStore.hasAnyRole(['Manager', 'Admin'])">
            <label class="form-label">Assignee</label>
            <select v-model="filters.assignee_id" @change="applyFilters" class="form-input">
              <option value="">All Assignees</option>
              <option v-for="user in agents" :key="user.id" :value="user.id">
                {{ user.name }}
              </option>
            </select>
          </div>
          <div class="flex items-end">
            <button @click="clearFilters" class="btn btn-secondary">
              Clear Filters
            </button>
          </div>
        </div>
      </div>

      <!-- Tickets Table -->
      <div class="mt-8 flex flex-col">
        <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <table class="table">
                <thead>
                  <tr>
                    <th>Ticket</th>
                    <th>Subject</th>
                    <th>Status</th>
                    <th>Priority</th>
                    <th>Requester</th>
                    <th v-if="!authStore.hasRole('End User')">Assignee</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                  <tr v-if="loading">
                    <td colspan="8" class="text-center py-4">
                      <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
                    </td>
                  </tr>
                  <tr v-else-if="tickets.length === 0">
                    <td colspan="8" class="text-center py-8 text-gray-500">
                      No tickets found
                    </td>
                  </tr>
                  <tr v-else v-for="ticket in tickets" :key="ticket.id">
                    <td class="font-medium text-indigo-600">
                      {{ ticket.number }}
                    </td>
                    <td class="max-w-xs truncate">
                      {{ ticket.subject }}
                    </td>
                    <td>
                      <span :class="getStatusBadgeClass(ticket.status)" class="badge">
                        {{ ticket.status }}
                      </span>
                    </td>
                    <td>
                      <span :class="getPriorityBadgeClass(ticket.priority)" class="badge">
                        {{ ticket.priority }}
                      </span>
                    </td>
                    <td>{{ ticket.requester?.name }}</td>
                    <td v-if="!authStore.hasRole('End User')">
                      {{ ticket.assignee?.name || 'Unassigned' }}
                    </td>
                    <td>{{ formatDate(ticket.created_at) }}</td>
                    <td>
                      <router-link
                        :to="`/tickets/${ticket.id}`"
                        class="text-indigo-600 hover:text-indigo-900"
                      >
                        View
                      </router-link>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import AppLayout from '@/components/AppLayout.vue'
import { useAuthStore } from '@/stores/auth'
import { useTicketsStore } from '@/stores/tickets'
import api from '@/services/api'

const authStore = useAuthStore()
const ticketsStore = useTicketsStore()

const agents = ref([])
const filters = ref({
  status: '',
  priority: '',
  assignee_id: ''
})

const tickets = computed(() => ticketsStore.tickets)
const loading = computed(() => ticketsStore.loading)

const getStatusBadgeClass = (status) => {
  const classes = {
    'New': 'badge-info',
    'In Progress': 'badge-warning',
    'Pending': 'badge-warning',
    'Resolved': 'badge-success',
    'Closed': 'badge-secondary'
  }
  return classes[status] || 'badge-secondary'
}

const getPriorityBadgeClass = (priority) => {
  const classes = {
    'Low': 'badge-secondary',
    'Medium': 'badge-info',
    'High': 'badge-warning',
    'Critical': 'badge-danger'
  }
  return classes[priority] || 'badge-secondary'
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}

const applyFilters = () => {
  ticketsStore.setFilters(filters.value)
  ticketsStore.fetchTickets()
}

const clearFilters = () => {
  filters.value = {
    status: '',
    priority: '',
    assignee_id: ''
  }
  ticketsStore.clearFilters()
  ticketsStore.fetchTickets()
}

const fetchAgents = async () => {
  try {
    const response = await api.get('/users', { params: { role: 'Agent' } })
    agents.value = response.data.users.data || response.data.users
  } catch (error) {
    console.error('Failed to fetch agents:', error)
  }
}

onMounted(async () => {
  await ticketsStore.fetchTickets()
  if (authStore.hasAnyRole(['Manager', 'Admin'])) {
    await fetchAgents()
  }
})
</script>
