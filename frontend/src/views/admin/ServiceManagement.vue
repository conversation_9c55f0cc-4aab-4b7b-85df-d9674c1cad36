<template>
  <AppLayout>
    <div class="px-4 py-6 sm:px-0">
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-2xl font-bold text-gray-900">Service Management</h1>
          <p class="mt-2 text-sm text-gray-700">
            Manage services and their configurations
          </p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <button
            @click="showCreateModal = true"
            class="btn btn-primary"
          >
            Add Service
          </button>
        </div>
      </div>

      <!-- Services Table -->
      <div class="mt-8 flex flex-col">
        <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <table class="table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Owner</th>
                    <th>Tickets</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                  <tr v-if="loading">
                    <td colspan="5" class="text-center py-4">
                      <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
                    </td>
                  </tr>
                  <tr v-else-if="services.length === 0">
                    <td colspan="5" class="text-center py-8 text-gray-500">
                      No services found
                    </td>
                  </tr>
                  <tr v-else v-for="service in services" :key="service.id">
                    <td class="font-medium text-gray-900">{{ service.name }}</td>
                    <td class="text-gray-500">{{ service.owner?.name }}</td>
                    <td class="text-gray-500">{{ service.tickets?.length || 0 }}</td>
                    <td class="text-gray-500">{{ formatDate(service.created_at) }}</td>
                    <td class="space-x-2">
                      <button
                        @click="editService(service)"
                        class="text-indigo-600 hover:text-indigo-900"
                      >
                        Edit
                      </button>
                      <button
                        @click="deleteService(service)"
                        class="text-red-600 hover:text-red-900"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Service Modal -->
    <div v-if="showCreateModal || showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            {{ showCreateModal ? 'Create Service' : 'Edit Service' }}
          </h3>
          <form @submit.prevent="saveService" class="space-y-4">
            <div>
              <label class="form-label">Service Name *</label>
              <input
                v-model="serviceForm.name"
                type="text"
                required
                class="form-input"
                placeholder="Service name"
              />
            </div>
            <div>
              <label class="form-label">Owner *</label>
              <select v-model="serviceForm.owner_id" required class="form-input">
                <option value="">Select owner</option>
                <option v-for="user in users" :key="user.id" :value="user.id">
                  {{ user.name }}
                </option>
              </select>
            </div>

            <div v-if="formErrors" class="text-red-600 text-sm">
              <div v-for="(error, field) in formErrors" :key="field">
                <div v-for="message in error" :key="message">
                  {{ message }}
                </div>
              </div>
            </div>

            <div class="flex justify-end space-x-3">
              <button
                type="button"
                @click="closeModal"
                class="btn btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                :disabled="formLoading"
                class="btn btn-primary"
              >
                <span v-if="formLoading">Saving...</span>
                <span v-else>{{ showCreateModal ? 'Create' : 'Update' }}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import AppLayout from '@/components/AppLayout.vue'
import api from '@/services/api'

const services = ref([])
const users = ref([])
const loading = ref(false)
const showCreateModal = ref(false)
const showEditModal = ref(false)
const formLoading = ref(false)
const formErrors = ref(null)
const editingService = ref(null)

const serviceForm = ref({
  name: '',
  owner_id: ''
})

const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}

const fetchServices = async () => {
  loading.value = true
  try {
    const response = await api.get('/services')
    services.value = response.data.services
  } catch (error) {
    console.error('Failed to fetch services:', error)
  } finally {
    loading.value = false
  }
}

const fetchUsers = async () => {
  try {
    const response = await api.get('/users')
    users.value = response.data.users.data || response.data.users
  } catch (error) {
    console.error('Failed to fetch users:', error)
  }
}

const editService = (service) => {
  editingService.value = service
  serviceForm.value = {
    name: service.name,
    owner_id: service.owner_id
  }
  showEditModal.value = true
}

const deleteService = async (service) => {
  if (confirm(`Are you sure you want to delete ${service.name}?`)) {
    try {
      await api.delete(`/services/${service.id}`)
      await fetchServices()
    } catch (error) {
      alert('Failed to delete service')
    }
  }
}

const saveService = async () => {
  formLoading.value = true
  formErrors.value = null

  try {
    if (showCreateModal.value) {
      await api.post('/services', serviceForm.value)
    } else {
      await api.put(`/services/${editingService.value.id}`, serviceForm.value)
    }
    
    await fetchServices()
    closeModal()
  } catch (error) {
    formErrors.value = error.response?.data?.errors || { general: ['An error occurred'] }
  } finally {
    formLoading.value = false
  }
}

const closeModal = () => {
  showCreateModal.value = false
  showEditModal.value = false
  editingService.value = null
  formErrors.value = null
  serviceForm.value = {
    name: '',
    owner_id: ''
  }
}

onMounted(async () => {
  await Promise.all([fetchServices(), fetchUsers()])
})
</script>
