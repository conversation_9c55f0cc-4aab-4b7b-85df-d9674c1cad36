<template>
  <AppLayout>
    <div class="px-4 py-6 sm:px-0">
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-2xl font-bold text-gray-900">User Management</h1>
          <p class="mt-2 text-sm text-gray-700">
            Manage system users and their roles
          </p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <button
            @click="showCreateModal = true"
            class="btn btn-primary"
          >
            Add User
          </button>
        </div>
      </div>

      <!-- Users Table -->
      <div class="mt-8 flex flex-col">
        <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <table class="table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Role</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                  <tr v-if="loading">
                    <td colspan="7" class="text-center py-4">
                      <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
                    </td>
                  </tr>
                  <tr v-else-if="users.length === 0">
                    <td colspan="7" class="text-center py-8 text-gray-500">
                      No users found
                    </td>
                  </tr>
                  <tr v-else v-for="user in users" :key="user.id">
                    <td class="font-medium text-gray-900">{{ user.name }}</td>
                    <td class="text-gray-500">{{ user.email }}</td>
                    <td class="text-gray-500">{{ user.phone || '-' }}</td>
                    <td>
                      <span v-for="role in user.roles" :key="role.id" class="badge badge-info mr-1">
                        {{ role.name }}
                      </span>
                    </td>
                    <td>
                      <span :class="user.status === 'active' ? 'badge-success' : 'badge-danger'" class="badge">
                        {{ user.status }}
                      </span>
                    </td>
                    <td class="text-gray-500">{{ formatDate(user.created_at) }}</td>
                    <td class="space-x-2">
                      <button
                        @click="editUser(user)"
                        class="text-indigo-600 hover:text-indigo-900"
                      >
                        Edit
                      </button>
                      <button
                        @click="deleteUser(user)"
                        class="text-red-600 hover:text-red-900"
                        :disabled="user.id === authStore.user?.id"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit User Modal -->
    <div v-if="showCreateModal || showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            {{ showCreateModal ? 'Create User' : 'Edit User' }}
          </h3>
          <form @submit.prevent="saveUser" class="space-y-4">
            <div>
              <label class="form-label">Name *</label>
              <input
                v-model="userForm.name"
                type="text"
                required
                class="form-input"
                placeholder="Full name"
              />
            </div>
            <div>
              <label class="form-label">Email *</label>
              <input
                v-model="userForm.email"
                type="email"
                required
                class="form-input"
                placeholder="Email address"
              />
            </div>
            <div>
              <label class="form-label">Phone</label>
              <input
                v-model="userForm.phone"
                type="tel"
                class="form-input"
                placeholder="Phone number"
              />
            </div>
            <div v-if="showCreateModal">
              <label class="form-label">Password *</label>
              <input
                v-model="userForm.password"
                type="password"
                required
                class="form-input"
                placeholder="Password"
              />
            </div>
            <div>
              <label class="form-label">Role *</label>
              <select v-model="userForm.role_id" required class="form-input">
                <option value="">Select role</option>
                <option v-for="role in roles" :key="role.id" :value="role.id">
                  {{ role.name }}
                </option>
              </select>
            </div>
            <div>
              <label class="form-label">Status</label>
              <select v-model="userForm.status" class="form-input">
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            <div v-if="formErrors" class="text-red-600 text-sm">
              <div v-for="(error, field) in formErrors" :key="field">
                <div v-for="message in error" :key="message">
                  {{ message }}
                </div>
              </div>
            </div>

            <div class="flex justify-end space-x-3">
              <button
                type="button"
                @click="closeModal"
                class="btn btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                :disabled="formLoading"
                class="btn btn-primary"
              >
                <span v-if="formLoading">Saving...</span>
                <span v-else>{{ showCreateModal ? 'Create' : 'Update' }}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import AppLayout from '@/components/AppLayout.vue'
import { useAuthStore } from '@/stores/auth'
import api from '@/services/api'

const authStore = useAuthStore()

const users = ref([])
const roles = ref([])
const loading = ref(false)
const showCreateModal = ref(false)
const showEditModal = ref(false)
const formLoading = ref(false)
const formErrors = ref(null)
const editingUser = ref(null)

const userForm = ref({
  name: '',
  email: '',
  phone: '',
  password: '',
  role_id: '',
  status: 'active'
})

const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}

const fetchUsers = async () => {
  loading.value = true
  try {
    const response = await api.get('/users')
    users.value = response.data.users.data || response.data.users
  } catch (error) {
    console.error('Failed to fetch users:', error)
  } finally {
    loading.value = false
  }
}

const fetchRoles = async () => {
  try {
    const response = await api.get('/roles')
    roles.value = response.data.roles
  } catch (error) {
    console.error('Failed to fetch roles:', error)
  }
}

const editUser = (user) => {
  editingUser.value = user
  userForm.value = {
    name: user.name,
    email: user.email,
    phone: user.phone || '',
    password: '',
    role_id: user.roles[0]?.id || '',
    status: user.status
  }
  showEditModal.value = true
}

const deleteUser = async (user) => {
  if (confirm(`Are you sure you want to delete ${user.name}?`)) {
    try {
      await api.delete(`/users/${user.id}`)
      await fetchUsers()
    } catch (error) {
      alert('Failed to delete user')
    }
  }
}

const saveUser = async () => {
  formLoading.value = true
  formErrors.value = null

  try {
    if (showCreateModal.value) {
      await api.post('/users', userForm.value)
    } else {
      await api.patch(`/users/${editingUser.value.id}`, userForm.value)
    }
    
    await fetchUsers()
    closeModal()
  } catch (error) {
    formErrors.value = error.response?.data?.errors || { general: ['An error occurred'] }
  } finally {
    formLoading.value = false
  }
}

const closeModal = () => {
  showCreateModal.value = false
  showEditModal.value = false
  editingUser.value = null
  formErrors.value = null
  userForm.value = {
    name: '',
    email: '',
    phone: '',
    password: '',
    role_id: '',
    status: 'active'
  }
}

onMounted(async () => {
  await Promise.all([fetchUsers(), fetchRoles()])
})
</script>
