<template>
  <AppLayout>
    <div class="px-4 py-6 sm:px-0">
      <div class="sm:flex sm:items-center">
        <div class="sm:flex-auto">
          <h1 class="text-2xl font-bold text-gray-900">SLA Management</h1>
          <p class="mt-2 text-sm text-gray-700">
            Manage Service Level Agreements
          </p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <button
            @click="showCreateModal = true"
            class="btn btn-primary"
          >
            Add SLA
          </button>
        </div>
      </div>

      <!-- SLAs Table -->
      <div class="mt-8 flex flex-col">
        <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <table class="table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Response Time</th>
                    <th>Resolution Time</th>
                    <th>Tickets</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                  <tr v-if="loading">
                    <td colspan="6" class="text-center py-4">
                      <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
                    </td>
                  </tr>
                  <tr v-else-if="slas.length === 0">
                    <td colspan="6" class="text-center py-8 text-gray-500">
                      No SLAs found
                    </td>
                  </tr>
                  <tr v-else v-for="sla in slas" :key="sla.id">
                    <td class="font-medium text-gray-900">{{ sla.name }}</td>
                    <td class="text-gray-500">{{ formatTime(sla.target_response_mins) }}</td>
                    <td class="text-gray-500">{{ formatTime(sla.target_resolution_mins) }}</td>
                    <td class="text-gray-500">{{ sla.tickets?.length || 0 }}</td>
                    <td class="text-gray-500">{{ formatDate(sla.created_at) }}</td>
                    <td class="space-x-2">
                      <button
                        @click="editSla(sla)"
                        class="text-indigo-600 hover:text-indigo-900"
                      >
                        Edit
                      </button>
                      <button
                        @click="deleteSla(sla)"
                        class="text-red-600 hover:text-red-900"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit SLA Modal -->
    <div v-if="showCreateModal || showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">
            {{ showCreateModal ? 'Create SLA' : 'Edit SLA' }}
          </h3>
          <form @submit.prevent="saveSla" class="space-y-4">
            <div>
              <label class="form-label">SLA Name *</label>
              <input
                v-model="slaForm.name"
                type="text"
                required
                class="form-input"
                placeholder="SLA name"
              />
            </div>
            <div>
              <label class="form-label">Target Response Time (minutes) *</label>
              <input
                v-model.number="slaForm.target_response_mins"
                type="number"
                required
                min="1"
                class="form-input"
                placeholder="Response time in minutes"
              />
              <p class="mt-1 text-sm text-gray-500">
                {{ formatTime(slaForm.target_response_mins) }}
              </p>
            </div>
            <div>
              <label class="form-label">Target Resolution Time (minutes) *</label>
              <input
                v-model.number="slaForm.target_resolution_mins"
                type="number"
                required
                min="1"
                class="form-input"
                placeholder="Resolution time in minutes"
              />
              <p class="mt-1 text-sm text-gray-500">
                {{ formatTime(slaForm.target_resolution_mins) }}
              </p>
            </div>

            <div v-if="formErrors" class="text-red-600 text-sm">
              <div v-for="(error, field) in formErrors" :key="field">
                <div v-for="message in error" :key="message">
                  {{ message }}
                </div>
              </div>
            </div>

            <div class="flex justify-end space-x-3">
              <button
                type="button"
                @click="closeModal"
                class="btn btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                :disabled="formLoading"
                class="btn btn-primary"
              >
                <span v-if="formLoading">Saving...</span>
                <span v-else>{{ showCreateModal ? 'Create' : 'Update' }}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import AppLayout from '@/components/AppLayout.vue'
import api from '@/services/api'

const slas = ref([])
const loading = ref(false)
const showCreateModal = ref(false)
const showEditModal = ref(false)
const formLoading = ref(false)
const formErrors = ref(null)
const editingSla = ref(null)

const slaForm = ref({
  name: '',
  target_response_mins: 60,
  target_resolution_mins: 480
})

const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}

const formatTime = (minutes) => {
  if (!minutes) return ''
  
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  
  if (hours === 0) {
    return `${mins} minutes`
  } else if (mins === 0) {
    return `${hours} hour${hours > 1 ? 's' : ''}`
  } else {
    return `${hours} hour${hours > 1 ? 's' : ''} ${mins} minutes`
  }
}

const fetchSlas = async () => {
  loading.value = true
  try {
    const response = await api.get('/slas')
    slas.value = response.data.slas
  } catch (error) {
    console.error('Failed to fetch SLAs:', error)
  } finally {
    loading.value = false
  }
}

const editSla = (sla) => {
  editingSla.value = sla
  slaForm.value = {
    name: sla.name,
    target_response_mins: sla.target_response_mins,
    target_resolution_mins: sla.target_resolution_mins
  }
  showEditModal.value = true
}

const deleteSla = async (sla) => {
  if (confirm(`Are you sure you want to delete ${sla.name}?`)) {
    try {
      await api.delete(`/slas/${sla.id}`)
      await fetchSlas()
    } catch (error) {
      alert('Failed to delete SLA')
    }
  }
}

const saveSla = async () => {
  formLoading.value = true
  formErrors.value = null

  try {
    if (showCreateModal.value) {
      await api.post('/slas', slaForm.value)
    } else {
      await api.put(`/slas/${editingSla.value.id}`, slaForm.value)
    }
    
    await fetchSlas()
    closeModal()
  } catch (error) {
    formErrors.value = error.response?.data?.errors || { general: ['An error occurred'] }
  } finally {
    formLoading.value = false
  }
}

const closeModal = () => {
  showCreateModal.value = false
  showEditModal.value = false
  editingSla.value = null
  formErrors.value = null
  slaForm.value = {
    name: '',
    target_response_mins: 60,
    target_resolution_mins: 480
  }
}

onMounted(() => {
  fetchSlas()
})
</script>
