<template>
  <AppLayout>
    <div class="px-4 py-6 sm:px-0">
      <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
        <p class="mt-1 text-sm text-gray-600">
          View system performance metrics and analytics
        </p>
      </div>

      <!-- Key Metrics -->
      <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Total Tickets</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ dashboardData.total_tickets || 0 }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Open Tickets</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ dashboardData.open_tickets || 0 }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Resolved Tickets</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ dashboardData.resolved_tickets || 0 }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">Closed Tickets</dt>
                  <dd class="text-lg font-medium text-gray-900">{{ dashboardData.closed_tickets || 0 }}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Performance Metrics -->
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-2 mb-8">
        <!-- MTTA -->
        <div class="bg-white shadow rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Mean Time To Acknowledge (MTTA)</h3>
          <div v-if="mttaLoading" class="text-center py-4">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
          </div>
          <div v-else class="space-y-2">
            <div class="text-3xl font-bold text-indigo-600">
              {{ mttaData.mtta_hours ? mttaData.mtta_hours.toFixed(1) : '0' }} hours
            </div>
            <div class="text-sm text-gray-500">
              {{ mttaData.mtta_minutes ? mttaData.mtta_minutes.toFixed(0) : '0' }} minutes
            </div>
          </div>
        </div>

        <!-- MTTR -->
        <div class="bg-white shadow rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Mean Time To Resolution (MTTR)</h3>
          <div v-if="mttrLoading" class="text-center py-4">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
          </div>
          <div v-else class="space-y-2">
            <div class="text-3xl font-bold text-green-600">
              {{ mttrData.mttr_hours ? mttrData.mttr_hours.toFixed(1) : '0' }} hours
            </div>
            <div class="text-sm text-gray-500">
              {{ mttrData.mttr_minutes ? mttrData.mttr_minutes.toFixed(0) : '0' }} minutes
            </div>
          </div>
        </div>
      </div>

      <!-- SLA Compliance -->
      <div class="bg-white shadow rounded-lg p-6 mb-8">
        <h3 class="text-lg font-medium text-gray-900 mb-4">SLA Compliance</h3>
        <div v-if="slaLoading" class="text-center py-4">
          <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600"></div>
        </div>
        <div v-else class="grid grid-cols-1 gap-4 sm:grid-cols-3">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{ slaData.total_tickets || 0 }}</div>
            <div class="text-sm text-gray-500">Total SLA Tickets</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ slaData.compliant_tickets || 0 }}</div>
            <div class="text-sm text-gray-500">Compliant Tickets</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-indigo-600">{{ slaData.compliance_rate || 0 }}%</div>
            <div class="text-sm text-gray-500">Compliance Rate</div>
          </div>
        </div>
      </div>

      <!-- Charts -->
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <!-- Tickets by Status -->
        <div class="bg-white shadow rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Tickets by Status</h3>
          <div class="space-y-3">
            <div v-for="(count, status) in dashboardData.tickets_by_status" :key="status" class="flex items-center justify-between">
              <div class="flex items-center">
                <span :class="getStatusBadgeClass(status)" class="badge mr-2">{{ status }}</span>
              </div>
              <span class="text-sm font-medium text-gray-900">{{ count }}</span>
            </div>
          </div>
        </div>

        <!-- Tickets by Priority -->
        <div class="bg-white shadow rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Tickets by Priority</h3>
          <div class="space-y-3">
            <div v-for="(count, priority) in dashboardData.tickets_by_priority" :key="priority" class="flex items-center justify-between">
              <div class="flex items-center">
                <span :class="getPriorityBadgeClass(priority)" class="badge mr-2">{{ priority }}</span>
              </div>
              <span class="text-sm font-medium text-gray-900">{{ count }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import AppLayout from '@/components/AppLayout.vue'
import api from '@/services/api'

const dashboardData = ref({})
const mttaData = ref({})
const mttrData = ref({})
const slaData = ref({})

const mttaLoading = ref(false)
const mttrLoading = ref(false)
const slaLoading = ref(false)

const getStatusBadgeClass = (status) => {
  const classes = {
    'New': 'badge-info',
    'In Progress': 'badge-warning',
    'Pending': 'badge-warning',
    'Resolved': 'badge-success',
    'Closed': 'badge-secondary'
  }
  return classes[status] || 'badge-secondary'
}

const getPriorityBadgeClass = (priority) => {
  const classes = {
    'Low': 'badge-secondary',
    'Medium': 'badge-info',
    'High': 'badge-warning',
    'Critical': 'badge-danger'
  }
  return classes[priority] || 'badge-secondary'
}

const fetchDashboardData = async () => {
  try {
    const response = await api.get('/reports/dashboard')
    dashboardData.value = response.data
  } catch (error) {
    console.error('Failed to fetch dashboard data:', error)
  }
}

const fetchMTTA = async () => {
  mttaLoading.value = true
  try {
    const response = await api.get('/reports/mtta')
    mttaData.value = response.data
  } catch (error) {
    console.error('Failed to fetch MTTA data:', error)
  } finally {
    mttaLoading.value = false
  }
}

const fetchMTTR = async () => {
  mttrLoading.value = true
  try {
    const response = await api.get('/reports/mttr')
    mttrData.value = response.data
  } catch (error) {
    console.error('Failed to fetch MTTR data:', error)
  } finally {
    mttrLoading.value = false
  }
}

const fetchSLACompliance = async () => {
  slaLoading.value = true
  try {
    const response = await api.get('/reports/sla-compliance')
    slaData.value = response.data
  } catch (error) {
    console.error('Failed to fetch SLA compliance data:', error)
  } finally {
    slaLoading.value = false
  }
}

onMounted(async () => {
  await Promise.all([
    fetchDashboardData(),
    fetchMTTA(),
    fetchMTTR(),
    fetchSLACompliance()
  ])
})
</script>
