import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/services/api'

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null)
  const token = ref(localStorage.getItem('token'))

  const isAuthenticated = computed(() => !!token.value)
  
  const userRoles = computed(() => {
    return user.value?.roles?.map(role => role.name) || []
  })

  const hasRole = (roleName) => {
    return userRoles.value.includes(roleName)
  }

  const hasAnyRole = (roleNames) => {
    return roleNames.some(role => userRoles.value.includes(role))
  }

  const hasPermission = (permission) => {
    if (!user.value?.roles) return false
    
    return user.value.roles.some(role => 
      role.permissions_json?.includes(permission)
    )
  }

  const login = async (credentials) => {
    try {
      const response = await api.post('/auth/login', credentials)
      const { user: userData, token: userToken } = response.data
      
      user.value = userData
      token.value = userToken
      localStorage.setItem('token', userToken)
      
      // Set default authorization header
      api.defaults.headers.common['Authorization'] = `Bearer ${userToken}`
      
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.error || 'Login failed' 
      }
    }
  }

  const register = async (userData) => {
    try {
      const response = await api.post('/auth/register', userData)
      const { user: newUser, token: userToken } = response.data
      
      user.value = newUser
      token.value = userToken
      localStorage.setItem('token', userToken)
      
      // Set default authorization header
      api.defaults.headers.common['Authorization'] = `Bearer ${userToken}`
      
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.errors || 'Registration failed' 
      }
    }
  }

  const logout = async () => {
    try {
      await api.post('/auth/logout')
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      user.value = null
      token.value = null
      localStorage.removeItem('token')
      delete api.defaults.headers.common['Authorization']
    }
  }

  const fetchUser = async () => {
    try {
      const response = await api.get('/auth/me')
      user.value = response.data.user
      return { success: true }
    } catch (error) {
      // Token might be invalid
      logout()
      return { success: false }
    }
  }

  // Initialize auth state
  const initAuth = async () => {
    if (token.value) {
      api.defaults.headers.common['Authorization'] = `Bearer ${token.value}`
      await fetchUser()
    }
  }

  return {
    user,
    token,
    isAuthenticated,
    userRoles,
    hasRole,
    hasAnyRole,
    hasPermission,
    login,
    register,
    logout,
    fetchUser,
    initAuth
  }
})
