import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '@/services/api'

export const useTicketsStore = defineStore('tickets', () => {
  const tickets = ref([])
  const currentTicket = ref(null)
  const loading = ref(false)
  const filters = ref({
    status: '',
    priority: '',
    assignee_id: ''
  })

  const fetchTickets = async (params = {}) => {
    loading.value = true
    try {
      const response = await api.get('/tickets', { params: { ...filters.value, ...params } })
      tickets.value = response.data.tickets.data || response.data.tickets
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.error || 'Failed to fetch tickets' 
      }
    } finally {
      loading.value = false
    }
  }

  const fetchTicket = async (id) => {
    loading.value = true
    try {
      const response = await api.get(`/tickets/${id}`)
      currentTicket.value = response.data.ticket
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.error || 'Failed to fetch ticket' 
      }
    } finally {
      loading.value = false
    }
  }

  const createTicket = async (ticketData) => {
    loading.value = true
    try {
      const response = await api.post('/tickets', ticketData)
      tickets.value.unshift(response.data.ticket)
      return { success: true, ticket: response.data.ticket }
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.errors || 'Failed to create ticket' 
      }
    } finally {
      loading.value = false
    }
  }

  const updateTicket = async (id, updates) => {
    loading.value = true
    try {
      const response = await api.patch(`/tickets/${id}`, updates)
      
      // Update in tickets list
      const index = tickets.value.findIndex(t => t.id === id)
      if (index !== -1) {
        tickets.value[index] = response.data.ticket
      }
      
      // Update current ticket if it's the same
      if (currentTicket.value?.id === id) {
        currentTicket.value = response.data.ticket
      }
      
      return { success: true, ticket: response.data.ticket }
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.errors || 'Failed to update ticket' 
      }
    } finally {
      loading.value = false
    }
  }

  const addComment = async (ticketId, comment) => {
    try {
      const response = await api.post(`/tickets/${ticketId}/comment`, { body: comment })
      
      // Add comment to current ticket if it's loaded
      if (currentTicket.value?.id === ticketId) {
        currentTicket.value.updates.push(response.data.comment)
      }
      
      return { success: true, comment: response.data.comment }
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.errors || 'Failed to add comment' 
      }
    }
  }

  const addAttachment = async (ticketId, file) => {
    try {
      const formData = new FormData()
      formData.append('file', file)
      
      const response = await api.post(`/tickets/${ticketId}/attachment`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      
      // Add attachment to current ticket if it's loaded
      if (currentTicket.value?.id === ticketId) {
        currentTicket.value.attachments.push(response.data.attachment)
      }
      
      return { success: true, attachment: response.data.attachment }
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.errors || 'Failed to upload attachment' 
      }
    }
  }

  const setFilters = (newFilters) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const clearFilters = () => {
    filters.value = {
      status: '',
      priority: '',
      assignee_id: ''
    }
  }

  return {
    tickets,
    currentTicket,
    loading,
    filters,
    fetchTickets,
    fetchTicket,
    createTicket,
    updateTicket,
    addComment,
    addAttachment,
    setFilters,
    clearFilters
  }
})
