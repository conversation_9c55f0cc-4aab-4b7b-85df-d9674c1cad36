import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/auth/LoginView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('@/views/auth/RegisterView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/dashboard',
      name: 'Dashboard',
      component: () => import('@/views/DashboardView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/tickets',
      name: 'Tickets',
      component: () => import('@/views/tickets/TicketListView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/tickets/create',
      name: 'CreateTicket',
      component: () => import('@/views/tickets/CreateTicketView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/tickets/:id',
      name: 'TicketDetail',
      component: () => import('@/views/tickets/TicketDetailView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/admin',
      name: 'Admin',
      component: () => import('@/views/admin/AdminDashboard.vue'),
      meta: { requiresAuth: true, requiresRole: 'Admin' }
    },
    {
      path: '/admin/users',
      name: 'AdminUsers',
      component: () => import('@/views/admin/UserManagement.vue'),
      meta: { requiresAuth: true, requiresRole: 'Admin' }
    },
    {
      path: '/admin/services',
      name: 'AdminServices',
      component: () => import('@/views/admin/ServiceManagement.vue'),
      meta: { requiresAuth: true, requiresRole: 'Admin' }
    },
    {
      path: '/admin/slas',
      name: 'AdminSLAs',
      component: () => import('@/views/admin/SlaManagement.vue'),
      meta: { requiresAuth: true, requiresRole: 'Admin' }
    },
    {
      path: '/reports',
      name: 'Reports',
      component: () => import('@/views/reports/ReportsView.vue'),
      meta: { requiresAuth: true, requiresRole: ['Manager', 'Admin'] }
    }
  ]
})

router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else if (to.meta.requiresGuest && authStore.isAuthenticated) {
    next('/dashboard')
  } else if (to.meta.requiresRole) {
    const requiredRoles = Array.isArray(to.meta.requiresRole) 
      ? to.meta.requiresRole 
      : [to.meta.requiresRole]
    
    if (!authStore.hasAnyRole(requiredRoles)) {
      next('/dashboard')
    } else {
      next()
    }
  } else {
    next()
  }
})

export default router
