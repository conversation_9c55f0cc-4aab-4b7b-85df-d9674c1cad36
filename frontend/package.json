{"name": "itsm-frontend", "version": "0.0.0", "private": true, "scripts": {"build": "vite build", "dev": "vite --host 0.0.0.0 --port 3000", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@vueuse/core": "^10.5.0", "axios": "^1.6.0", "pinia": "^2.1.7", "vue": "^3.3.8", "vue-router": "^4.2.5", "@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "tailwindcss": "^3.3.6"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/node": "^18.18.5", "@vitejs/plugin-vue": "^4.4.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "autoprefixer": "^10.4.16", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "npm-run-all2": "^6.1.1", "postcss": "^8.4.31", "prettier": "^3.0.3", "typescript": "~5.2.0", "vite": "^4.4.11", "vue-tsc": "^1.8.19"}}