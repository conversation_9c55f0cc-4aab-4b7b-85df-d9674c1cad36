version: '3.8'

services:
  # Laravel Backend
  app:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: itsm_app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./backend:/var/www
      - ./backend/storage/app/public:/var/www/storage/app/public
    networks:
      - itsm_network
    depends_on:
      - db
      - redis
    environment:
      - DB_CONNECTION=pgsql
      - DB_HOST=db
      - DB_PORT=5432
      - DB_DATABASE=itsm_db
      - DB_USERNAME=itsm_user
      - DB_PASSWORD=itsm_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379

  # Nginx Web Server
  webserver:
    image: nginx:alpine
    container_name: itsm_webserver
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./backend:/var/www
      - ./docker/nginx:/etc/nginx/conf.d
    networks:
      - itsm_network
    depends_on:
      - app

  # PostgreSQL Database
  db:
    image: postgres:15
    container_name: itsm_db
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: itsm_db
      POSTGRES_USER: itsm_user
      POSTGRES_PASSWORD: itsm_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - itsm_network

  # Redis for Queue and Cache
  redis:
    image: redis:7-alpine
    container_name: itsm_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - itsm_network

  # Vue.js Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: itsm_frontend
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - itsm_network
    environment:
      - VITE_API_URL=http://localhost:8000/api

  # Queue Worker
  queue:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: itsm_queue
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./backend:/var/www
    networks:
      - itsm_network
    depends_on:
      - db
      - redis
    command: php artisan queue:work --sleep=3 --tries=3
    environment:
      - DB_CONNECTION=pgsql
      - DB_HOST=db
      - DB_PORT=5432
      - DB_DATABASE=itsm_db
      - DB_USERNAME=itsm_user
      - DB_PASSWORD=itsm_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379

volumes:
  postgres_data:

networks:
  itsm_network:
    driver: bridge
