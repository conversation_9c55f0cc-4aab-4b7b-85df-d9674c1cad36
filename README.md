# ITSM Platform

یک پلتفرم جامع مدیریت خدمات فناوری اطلاعات (ITSM) مشابه ServiceNow که با Laravel 11 و Vue 3 ساخته شده است.

## ویژگی‌های کلیدی

### Backend (Laravel 11 + PHP 8.2)
- ✅ احراز هویت JWT
- ✅ سیستم کنترل دسترسی مبتنی بر نقش (RBAC)
- ✅ مدیریت تیکت کامل (CRUD، کامنت، پیوست، تغییر وضعیت)
- ✅ مدیریت سرویس‌ها و SLA
- ✅ سیستم Audit Logging
- ✅ اعلان‌های ایمیل با Queue
- ✅ API مستندسازی شده با Swagger/OpenAPI

### Frontend (Vue 3 + Vite + Pinia + Vue Router)
- ✅ داشبورد متفاوت برای هر نقش (End User، Agent، Manager، Admin)
- ✅ فرم‌های ایجاد و مدیریت تیکت
- ✅ صفحات مدیریت کاربران، سرویس‌ها و SLA
- ✅ گزارش‌های تحلیلی (MTTA، MTTR، SLA Compliance)
- ✅ طراحی Responsive با Tailwind CSS

### پایگاه داده
- ✅ PostgreSQL
- ✅ روابط کامل بین مدل‌ها
- ✅ Migration های کامل

### سایر قابلیت‌ها
- ✅ Docker Compose برای توسعه محلی
- ✅ Redis برای Queue و Cache
- ✅ سیستم اعلان‌های ایمیل
- ✅ مستندات API

## نقش‌ها و مجوزها

### End User
- ایجاد تیکت
- مشاهده تیکت‌های خود

### Agent
- مشاهده تیکت‌های اختصاص یافته
- به‌روزرسانی وضعیت تیکت
- اضافه کردن کامنت

### Manager
- مشاهده گزارش‌ها
- اختصاص تیکت‌ها
- تمام مجوزهای Agent

### Admin
- دسترسی کامل
- مدیریت کاربران
- مدیریت سرویس‌ها
- مدیریت SLA
- تمام مجوزهای سایر نقش‌ها

## راه‌اندازی

### پیش‌نیازها
- Docker و Docker Compose
- Git

### مراحل نصب

1. **کلون کردن پروژه:**
```bash
git clone <repository-url>
cd itsm-platform
```

2. **راه‌اندازی با Docker:**
```bash
docker-compose up -d
```

3. **نصب dependencies و راه‌اندازی Laravel:**
```bash
# ورود به container
docker-compose exec app bash

# نصب dependencies
composer install

# تولید کلید اپلیکیشن
php artisan key:generate

# تولید کلید JWT
php artisan jwt:secret

# اجرای migration ها
php artisan migrate

# اجرای seeder ها
php artisan db:seed
```

4. **راه‌اندازی Frontend:**
```bash
# ورود به container frontend
docker-compose exec frontend sh

# نصب dependencies
npm install

# شروع development server
npm run dev
```

## دسترسی به سیستم

### URLs
- **Frontend:** http://localhost:3000
- **Backend API:** http://localhost:8000/api
- **API Documentation:** http://localhost:8000/api/documentation

### حساب‌های کاربری پیش‌فرض

| نقش | ایمیل | رمز عبور |
|-----|-------|----------|
| Admin | <EMAIL> | admin123 |
| Manager | <EMAIL> | manager123 |
| Agent | <EMAIL> | agent123 |
| End User | <EMAIL> | user123 |

## API Endpoints

### Authentication
- `POST /api/auth/register` - ثبت‌نام کاربر جدید
- `POST /api/auth/login` - ورود کاربر
- `POST /api/auth/logout` - خروج کاربر
- `GET /api/auth/me` - اطلاعات کاربر فعلی

### Tickets
- `GET /api/tickets` - لیست تیکت‌ها
- `POST /api/tickets` - ایجاد تیکت جدید
- `GET /api/tickets/{id}` - جزئیات تیکت
- `PATCH /api/tickets/{id}` - به‌روزرسانی تیکت
- `POST /api/tickets/{id}/comment` - اضافه کردن کامنت
- `POST /api/tickets/{id}/attachment` - اضافه کردن پیوست

### Services
- `GET /api/services` - لیست سرویس‌ها
- `POST /api/services` - ایجاد سرویس جدید (Admin)
- `PUT /api/services/{id}` - به‌روزرسانی سرویس (Admin)
- `DELETE /api/services/{id}` - حذف سرویس (Admin)

### SLAs
- `GET /api/slas` - لیست SLA ها
- `POST /api/slas` - ایجاد SLA جدید (Admin)
- `PUT /api/slas/{id}` - به‌روزرسانی SLA (Admin)
- `DELETE /api/slas/{id}` - حذف SLA (Admin)

### Users
- `GET /api/users` - لیست کاربران (Admin)
- `POST /api/users` - ایجاد کاربر جدید (Admin)
- `PATCH /api/users/{id}` - به‌روزرسانی کاربر (Admin)
- `DELETE /api/users/{id}` - حذف کاربر (Admin)

### Reports
- `GET /api/reports/dashboard` - آمار کلی
- `GET /api/reports/mtta` - میانگین زمان پاسخ
- `GET /api/reports/mttr` - میانگین زمان حل مسئله
- `GET /api/reports/sla-compliance` - میزان رعایت SLA

## ساختار پروژه

```
├── backend/                 # Laravel Backend
│   ├── app/
│   │   ├── Http/Controllers/
│   │   ├── Models/
│   │   ├── Mail/
│   │   ├── Events/
│   │   └── Listeners/
│   ├── database/
│   │   ├── migrations/
│   │   └── seeders/
│   └── routes/
├── frontend/               # Vue.js Frontend
│   ├── src/
│   │   ├── components/
│   │   ├── views/
│   │   ├── stores/
│   │   ├── router/
│   │   └── services/
│   └── public/
├── docker/                # Docker configurations
└── docker-compose.yml    # Docker Compose file
```

## تکنولوژی‌های استفاده شده

### Backend
- **Laravel 11** - PHP Framework
- **PHP 8.2** - Programming Language
- **PostgreSQL** - Database
- **Redis** - Cache & Queue
- **JWT** - Authentication
- **Swagger/OpenAPI** - API Documentation

### Frontend
- **Vue 3** - JavaScript Framework
- **Vite** - Build Tool
- **Pinia** - State Management
- **Vue Router** - Routing
- **Tailwind CSS** - CSS Framework
- **Axios** - HTTP Client

### DevOps
- **Docker** - Containerization
- **Docker Compose** - Multi-container orchestration
- **Nginx** - Web Server

## مشارکت

برای مشارکت در این پروژه:

1. Fork کنید
2. Branch جدید ایجاد کنید (`git checkout -b feature/amazing-feature`)
3. تغییرات را commit کنید (`git commit -m 'Add some amazing feature'`)
4. Push کنید (`git push origin feature/amazing-feature`)
5. Pull Request ایجاد کنید

## لایسنس

این پروژه تحت لایسنس MIT منتشر شده است.

## پشتیبانی

برای گزارش مشکلات یا درخواست ویژگی‌های جدید، لطفاً از بخش Issues استفاده کنید.
